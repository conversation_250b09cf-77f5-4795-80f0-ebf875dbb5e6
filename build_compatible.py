#!/usr/bin/env python3
"""
Excel处理工具 - 兼容性优化打包脚本
解决PyInstaller PKG归档加载问题
"""

import os
import sys
import subprocess
import shutil

def clean_previous_builds():
    """清理之前的构建文件"""
    print("正在清理之前的构建文件...")
    
    # 删除dist目录
    if os.path.exists("dist"):
        shutil.rmtree("dist")
        print("已删除dist目录")
    
    # 删除build目录
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("已删除build目录")
    
    # 删除spec文件
    for spec_file in ["ExcelProcessor.spec", "Excel处理工具.spec", "process_excel.spec"]:
        if os.path.exists(spec_file):
            os.remove(spec_file)
            print(f"已删除{spec_file}")

def build_compatible_exe():
    """使用兼容性优化的参数打包exe文件"""
    print("正在使用兼容性优化参数打包exe文件...")
    
    # 使用最基本的PyInstaller参数，避免复杂配置
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onedir",  # 使用目录模式，更稳定
        "--windowed",  # 无控制台窗口
        "--name=ExcelProcessor",  # 英文名称
        "--noconfirm",  # 不询问覆盖
        "--clean",  # 清理缓存
        "--distpath=dist",  # 指定输出目录
        "--workpath=build",  # 指定工作目录
        "process_excel.py"
    ]
    
    try:
        print("执行命令:", " ".join(cmd))
        subprocess.check_call(cmd)
        print("打包完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False

def create_launcher_script():
    """创建启动脚本，进一步提高兼容性"""
    launcher_content = '''@echo off
cd /d "%~dp0"
start "" "ExcelProcessor.exe"
'''
    
    launcher_path = os.path.join("dist", "ExcelProcessor", "启动Excel处理工具.bat")
    try:
        with open(launcher_path, 'w', encoding='gbk') as f:
            f.write(launcher_content)
        print(f"已创建启动脚本: {launcher_path}")
    except Exception as e:
        print(f"创建启动脚本失败: {e}")

def verify_build():
    """验证构建结果"""
    exe_path = os.path.join("dist", "ExcelProcessor", "ExcelProcessor.exe")
    
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"\n✅ 构建成功!")
        print(f"📁 程序位置: {os.path.abspath(exe_path)}")
        print(f"📊 文件大小: {file_size:.2f} MB")
        
        # 列出dist目录内容
        dist_path = os.path.join("dist", "ExcelProcessor")
        if os.path.exists(dist_path):
            print(f"\n📂 程序文件夹内容:")
            for item in os.listdir(dist_path):
                item_path = os.path.join(dist_path, item)
                if os.path.isfile(item_path):
                    size = os.path.getsize(item_path) / 1024
                    print(f"   📄 {item} ({size:.1f} KB)")
                else:
                    print(f"   📁 {item}/")
        
        return True
    else:
        print("\n❌ 构建失败: 未找到生成的exe文件")
        return False

def main():
    print("=" * 60)
    print("Excel处理工具 - 兼容性优化打包脚本")
    print("=" * 60)
    
    # 检查必要文件
    if not os.path.exists("process_excel.py"):
        print("❌ 错误: 找不到process_excel.py文件")
        return
    
    # 清理之前的构建
    clean_previous_builds()
    
    # 执行打包
    if not build_compatible_exe():
        return
    
    # 创建启动脚本
    create_launcher_script()
    
    # 验证构建结果
    if verify_build():
        print("\n" + "=" * 60)
        print("🎉 打包完成!")
        print("=" * 60)
        print("\n📋 使用说明:")
        print("1. 将整个 'ExcelProcessor' 文件夹复制到目标电脑")
        print("2. 双击 'ExcelProcessor.exe' 或 '启动Excel处理工具.bat' 运行")
        print("3. 如果遇到问题，请尝试:")
        print("   - 右键以管理员身份运行")
        print("   - 临时关闭杀毒软件")
        print("   - 将文件夹放在C盘根目录等简单路径")
        print("\n⚠️  重要提示:")
        print("- 必须保持整个文件夹完整，不要单独复制exe文件")
        print("- 避免将程序放在包含中文或特殊字符的路径中")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
