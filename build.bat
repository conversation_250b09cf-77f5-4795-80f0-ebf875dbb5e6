@echo off
:: 设置代码页为UTF-8，但不显示输出
chcp 65001 >nul 2>&1
:: 如果UTF-8失败，尝试使用GBK
if errorlevel 1 chcp 936 >nul 2>&1
title Excel Tool - Auto Build Script

echo ============================================================
echo                Excel Tool - Auto Build Script
echo ============================================================
echo.
echo This script will automatically complete the following operations:
echo 1. Check Python environment
echo 2. Install necessary dependencies
echo 3. Use PyInstaller to package into exe file
echo 4. Clean temporary files
echo.
echo Please make sure you have installed Python 3.7 or higher
echo.
pause

echo.
echo [1/4] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo X Error: Python environment not found
    echo.
    echo Please install Python 3.7 or higher first:
    echo https://www.python.org/downloads/
    echo.
    echo Please check "Add Python to PATH" option during installation
    pause
    exit /b 1
)

python --version
echo √ Python environment check passed

echo.
echo [2/4] Installing dependencies...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo X Error: Failed to install dependencies
    echo.
    echo Possible solutions:
    echo 1. Check network connection
    echo 2. Use China mirror: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    echo 3. Run this script as administrator
    pause
    exit /b 1
)
echo √ Dependencies installation completed

echo.
echo [3/4] Packaging exe file...
echo This may take a few minutes, please wait patiently...
python -m PyInstaller --onefile --windowed --name="ExcelProcessor" --hidden-import=pandas --hidden-import=openpyxl --hidden-import=tkinter --hidden-import=tkinter.filedialog --hidden-import=tkinter.messagebox --collect-all=pandas --collect-all=openpyxl --noconfirm process_excel.py

if errorlevel 1 (
    echo X Error: Packaging failed
    echo.
    echo Possible solutions:
    echo 1. Make sure all dependencies are installed correctly
    echo 2. Check if antivirus software is blocking
    echo 3. Try running as administrator
    pause
    exit /b 1
)
echo √ Packaging completed

echo.
echo [4/4] Cleaning temporary files...
if exist build (
    rmdir /s /q build
    echo √ Deleted build directory
)
if exist "ExcelProcessor.spec" (
    del "ExcelProcessor.spec"
    echo √ Deleted spec file
)

echo.
echo ============================================================
echo                    Build Completed!
echo ============================================================
echo.

if exist "dist\ExcelProcessor.exe" (
    echo √ Executable file generated: dist\ExcelProcessor.exe
    echo.
    echo File information:
    for %%i in ("dist\ExcelProcessor.exe") do echo    Size: %%~zi bytes
    echo    Location: %CD%\dist\ExcelProcessor.exe
    echo.
    echo Usage instructions:
    echo 1. Copy 'ExcelProcessor.exe' to target computer
    echo 2. Double-click to run the exe file
    echo 3. Follow prompts to select Excel file and save location
    echo 4. Wait for processing to complete
    echo.
    echo Tips:
    echo - Supports .xlsx and .xls Excel file formats
    echo - Program will automatically delete specified columns
    echo - All formulas will be converted to values
    echo - Original file will not be modified
) else (
    echo X Warning: Generated exe file not found
    echo Please check files in dist directory
    if exist dist (
        echo.
        echo dist directory contents:
        dir dist
    )
)

echo.
echo ============================================================
echo Press any key to exit...
pause >nul
