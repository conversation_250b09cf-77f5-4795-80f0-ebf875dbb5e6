#!/usr/bin/env python3
"""
Excel处理工具打包脚本
用于将process_excel.py打包成Windows可执行文件
"""

import os
import sys
import subprocess
import shutil

def install_dependencies():
    """安装必要的依赖"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装完成！")
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False
    return True

def build_exe():
    """使用PyInstaller打包exe文件"""
    print("正在打包exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        sys.executable, "-m", "PyInstaller",  # 使用python -m PyInstaller
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--name=Excel处理工具",  # 设置exe文件名
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=tkinter",
        "process_excel.py"
    ]
    
    # 添加图标文件（如果存在）
    if os.path.exists("icon.ico"):
        cmd.extend(["--icon=icon.ico"])
    
    try:
        subprocess.check_call(cmd)
        print("打包完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False

def clean_build_files():
    """清理构建过程中产生的临时文件"""
    print("正在清理临时文件...")
    
    # 删除build目录
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("已删除build目录")
    
    # 删除spec文件
    spec_file = "Excel处理工具.spec"
    if os.path.exists(spec_file):
        os.remove(spec_file)
        print(f"已删除{spec_file}")

def main():
    print("=" * 50)
    print("Excel处理工具 - 打包脚本")
    print("=" * 50)
    
    # 检查必要文件是否存在
    if not os.path.exists("process_excel.py"):
        print("错误: 找不到process_excel.py文件")
        return
    
    if not os.path.exists("requirements.txt"):
        print("错误: 找不到requirements.txt文件")
        return
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 打包exe
    if not build_exe():
        return
    
    # 清理临时文件
    clean_build_files()
    
    print("\n" + "=" * 50)
    print("打包完成！")

    # 检查输出文件是否存在（跨平台）
    possible_files = [
        os.path.join("dist", "Excel处理工具.exe"),  # Windows
        os.path.join("dist", "Excel处理工具"),      # Linux
        os.path.join("dist", "Excel处理工具.app")   # macOS
    ]

    found_file = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            found_file = file_path
            break

    if found_file:
        print(f"可执行文件位置: {found_file}")
        if os.path.isfile(found_file):
            file_size = os.path.getsize(found_file) / (1024 * 1024)  # 转换为MB
            print(f"文件大小: {file_size:.2f} MB")
        elif os.path.isdir(found_file):  # macOS .app bundle
            print("生成了应用程序包")
    else:
        print("警告: 未找到生成的可执行文件")

    print("=" * 50)

if __name__ == "__main__":
    main()
