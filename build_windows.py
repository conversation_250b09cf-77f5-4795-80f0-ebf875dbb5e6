#!/usr/bin/env python3
"""
Excel处理工具 - Windows专用打包脚本
用于在Windows系统上将process_excel.py打包成exe文件
"""

import os
import sys
import subprocess
import shutil

def install_dependencies():
    """安装必要的依赖"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装完成！")
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False
    return True

def build_windows_exe():
    """使用PyInstaller打包Windows exe文件"""
    print("正在打包Windows exe文件...")

    # PyInstaller命令参数 - 使用目录模式提高兼容性
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onedir",  # 打包成目录（更稳定）
        "--windowed",  # 不显示控制台窗口
        "--name=ExcelProcessor",  # 使用英文名避免编码问题
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--add-data=process_excel.py;.",  # 确保主文件被包含
        "--noconfirm",  # 不询问覆盖
        "--clean",  # 清理缓存
        "process_excel.py"
    ]
    
    # 添加图标文件（如果存在）
    if os.path.exists("icon.ico"):
        cmd.extend(["--icon=icon.ico"])
    
    try:
        subprocess.check_call(cmd)
        print("打包完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False

def clean_build_files():
    """清理构建过程中产生的临时文件"""
    print("正在清理临时文件...")
    
    # 删除build目录
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("已删除build目录")
    
    # 删除spec文件
    spec_file = "Excel处理工具.spec"
    if os.path.exists(spec_file):
        os.remove(spec_file)
        print(f"已删除{spec_file}")

def create_icon():
    """创建一个简单的图标文件（可选）"""
    # 这里可以添加创建图标的代码
    # 或者提示用户添加icon.ico文件
    if not os.path.exists("icon.ico"):
        print("提示: 如果您有图标文件，请将其命名为 'icon.ico' 并放在当前目录中")

def main():
    print("=" * 60)
    print("Excel处理工具 - Windows专用打包脚本")
    print("=" * 60)
    
    # 检查操作系统
    if os.name != 'nt' and 'win' not in sys.platform.lower():
        print("警告: 此脚本专为Windows系统设计")
        print("当前系统:", sys.platform)
        response = input("是否继续？(y/N): ")
        if response.lower() != 'y':
            return
    
    # 检查必要文件是否存在
    if not os.path.exists("process_excel.py"):
        print("错误: 找不到process_excel.py文件")
        return
    
    if not os.path.exists("requirements.txt"):
        print("错误: 找不到requirements.txt文件")
        return
    
    # 创建图标提示
    create_icon()
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 打包exe
    if not build_windows_exe():
        return
    
    # 清理临时文件
    clean_build_files()
    
    print("\n" + "=" * 60)
    print("打包完成！")
    
    # 检查输出文件是否存在
    exe_path = os.path.join("dist", "ExcelProcessor", "ExcelProcessor.exe")
    dist_folder = os.path.join("dist", "ExcelProcessor")

    if os.path.exists(exe_path):
        print(f"可执行文件位置: {exe_path}")
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # 转换为MB
        print(f"文件大小: {file_size:.2f} MB")
        print(f"程序文件夹: {dist_folder}")
        print("\n使用说明:")
        print("1. 将整个 'ExcelProcessor' 文件夹复制到目标Windows电脑")
        print("2. 进入文件夹，双击 'ExcelProcessor.exe' 运行")
        print("3. 按照提示选择Excel文件和保存位置")
        print("4. 等待处理完成")
        print("\n注意: 请保持文件夹完整，不要单独复制exe文件")
    else:
        print("警告: 未找到生成的exe文件")
        print("请检查dist目录中的文件")
        if os.path.exists("dist"):
            print("\ndist目录内容:")
            for item in os.listdir("dist"):
                print(f"  {item}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
